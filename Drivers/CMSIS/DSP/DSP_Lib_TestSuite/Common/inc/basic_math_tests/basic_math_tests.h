#ifndef _BASIC_MATH_TESTS_H_
#define _BASIC_MATH_TESTS_H_

/*--------------------------------------------------------------------------------*/
/* Test/Group Declarations */
/*--------------------------------------------------------------------------------*/
JTEST_DECLARE_GROUP(abs_tests);
JTEST_DECLARE_GROUP(add_tests);
JTEST_DECLARE_GROUP(dot_prod_tests);
JTEST_DECLARE_GROUP(mult_tests);
JTEST_DECLARE_GROUP(negate_tests);
JTEST_DECLARE_GROUP(offset_tests);
JTEST_DECLARE_GROUP(scale_tests);
JTEST_DECLARE_GROUP(shift_tests);
JTEST_DECLARE_GROUP(sub_tests);

#endif /* _BASIC_MATH_TESTS_H_ */
