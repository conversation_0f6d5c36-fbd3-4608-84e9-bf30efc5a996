{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Flash", // 任务名称
            "type": "process", // 任务类型
            "command": "pyocd", // 执行的命令
            "args": [  // 命令的参数
              "flash",
              "./build/mc02_test.hex",
              "-t",
              "stm32h723vgtx",
            ],
            "problemMatcher": [],// 问题匹配器
            "group": {
              "kind": "build",
              "isDefault": true
            },
            "presentation": { // 任务的展示方式
              "reveal": "always",
              "panel": "dedicated",
              "clear": true
            },
            "detail": "Flash mc02_test.hex to STM32 via DAPLink (pyocd)"
          }
    ]
}